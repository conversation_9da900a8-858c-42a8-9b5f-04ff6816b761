<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bryzos Theme Demo</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;600;700;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="src/renderer/styles.css">
    <style>
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .theme-info {
            background: var(--bg-card, white);
            padding: 1rem;
            border-radius: var(--border-radius-md);
            margin-bottom: 2rem;
            box-shadow: var(--shadow-md);
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .demo-card {
            background: var(--bg-card, white);
            padding: 1.5rem;
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-md);
        }
        
        .demo-buttons {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 class="app-title">Bryzos Theme System Demo</h1>
        <p class="app-subtitle">Test the automatic Windows theme detection and manual theme switching</p>
        
        <div class="theme-info">
            <h3>Current Theme Information</h3>
            <p><strong>Selected Theme:</strong> <span id="current-theme">Loading...</span></p>
            <p><strong>Effective Theme:</strong> <span id="effective-theme">Loading...</span></p>
            <p><strong>System Theme:</strong> <span id="system-theme">Loading...</span></p>
        </div>
        
        <div class="demo-grid">
            <div class="demo-card">
                <h3 class="status-title">Status Card Example</h3>
                <div class="status-indicator success"></div>
                <p>This demonstrates how status cards look in different themes.</p>
                <div class="demo-buttons">
                    <button class="btn btn-primary">Primary Button</button>
                    <button class="btn btn-secondary">Secondary Button</button>
                </div>
            </div>
            
            <div class="demo-card">
                <h3 class="status-title">Form Elements</h3>
                <div class="form-group">
                    <label class="form-label">Sample Input</label>
                    <input type="text" class="form-input" placeholder="Type something..." value="Sample text">
                </div>
                <div class="demo-buttons">
                    <button class="btn btn-success">Success</button>
                    <button class="btn btn-warning">Warning</button>
                    <button class="btn btn-danger">Danger</button>
                </div>
            </div>
            
            <div class="demo-card">
                <h3 class="status-title">Test Results Style</h3>
                <div class="test-results">
                    <div class="test-results-header">
                        <span>Sample Test Results</span>
                    </div>
                    <div class="test-results-body">
                        <div class="test-item">
                            <div class="test-info">
                                <div class="test-id">TEST-001</div>
                                <div class="test-name">Connectivity Test</div>
                            </div>
                            <div class="test-status">
                                <div class="status-indicator success"></div>
                                <span class="test-status-text success">PASS</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="demo-card">
            <h3>Theme Controls</h3>
            <p>Use the floating theme toggle button (🌙/☀️/🌓) in the top-right corner to switch themes.</p>
            <p>Or use these buttons:</p>
            <div class="demo-buttons">
                <button class="btn btn-primary" onclick="setTheme('light')">Light Theme</button>
                <button class="btn btn-primary" onclick="setTheme('dark')">Dark Theme</button>
                <button class="btn btn-secondary" onclick="setTheme('auto')">Auto Theme</button>
            </div>
        </div>
    </div>

    <script>
        // Mock electronAPI for browser testing
        if (!window.electronAPI) {
            window.electronAPI = {
                onThemeChange: (callback) => {
                    // Simulate theme changes for demo
                    console.log('Mock electronAPI: onThemeChange registered');
                }
            };
        }
        
        function setTheme(theme) {
            if (window.themeManager) {
                window.themeManager.setTheme(theme);
            }
        }
        
        function updateThemeInfo() {
            if (window.themeManager) {
                const info = window.themeManager.getThemeInfo();
                document.getElementById('current-theme').textContent = info.currentTheme;
                document.getElementById('effective-theme').textContent = info.effectiveTheme;
                document.getElementById('system-theme').textContent = info.systemTheme;
            }
        }
        
        // Update theme info when theme changes
        document.addEventListener('themechange', updateThemeInfo);
        
        // Update theme info when page loads
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(updateThemeInfo, 100); // Small delay to ensure theme manager is initialized
        });
    </script>
    
    <script src="src/renderer/theme-manager.js"></script>
</body>
</html>
