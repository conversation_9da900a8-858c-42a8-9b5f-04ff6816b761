# Bryzos Theme System

This document explains the theme system implemented for the Bryzos Connectivity Troubleshooter, which provides automatic Windows theme detection and manual theme switching.

## Features

- **Automatic Windows Theme Detection**: Automatically detects and applies Windows dark/light theme changes
- **Manual Theme Switching**: Users can manually override the theme with light, dark, or auto modes
- **Persistent Theme Preferences**: Theme choices are saved and restored between sessions
- **Smooth Transitions**: CSS transitions provide smooth theme switching animations
- **Comprehensive Coverage**: All UI components are theme-aware

## Files Modified/Added

### New Files
- `src/renderer/theme-manager.js` - Main theme management logic
- `theme-demo.html` - Demo page to test theme functionality
- `THEME_SYSTEM.md` - This documentation file

### Modified Files
- `src/renderer/styles.css` - Added comprehensive dark theme support
- `src/renderer/index.html` - Added theme-manager.js script
- `src/main.js` - Added native theme detection using Electron's nativeTheme API
- `src/preload.js` - Added theme change event bridge

## How It Works

### 1. Theme Detection
The system uses multiple methods to detect theme preferences:

- **System Theme**: Uses CSS `prefers-color-scheme` media query and Electron's `nativeTheme` API
- **Manual Override**: Users can set explicit light/dark themes
- **Auto Mode**: Follows system theme changes automatically

### 2. Theme Application
Themes are applied using CSS custom properties (variables) and data attributes:

```css
/* Light theme (default) */
:root {
  --rich-black: #0d1b2aff;
  --platinum: #e0e1ddff;
  /* ... other variables */
}

/* Dark theme */
[data-theme="dark"] {
  --rich-black: #e2e8f0;
  --platinum: #1a202c;
  /* ... other variables */
}
```

### 3. Theme Persistence
Theme preferences are stored in `localStorage` and automatically restored on app startup.

## Usage

### For Users

1. **Automatic Theme**: By default, the app follows your Windows theme setting
2. **Manual Override**: Click the theme toggle button (🌙/☀️/🌓) in the top-right corner
3. **Theme Options**:
   - ☀️ Light theme (always light)
   - 🌙 Dark theme (always dark)
   - 🌓 Auto theme (follows Windows setting)

### For Developers

#### Basic Usage
```javascript
// Get theme manager instance
const themeManager = window.themeManager;

// Set theme manually
themeManager.setTheme('dark');   // Force dark theme
themeManager.setTheme('light');  // Force light theme
themeManager.setTheme('auto');   // Follow system theme

// Get current theme info
const info = themeManager.getThemeInfo();
console.log(info.currentTheme);    // 'light', 'dark', or 'auto'
console.log(info.effectiveTheme);  // actual theme being used
console.log(info.systemTheme);     // system's preferred theme
```

#### Listen for Theme Changes
```javascript
document.addEventListener('themechange', (event) => {
  console.log('Theme changed:', event.detail);
  // event.detail contains: { theme, effectiveTheme, systemTheme }
});
```

#### Adding Theme Support to New Components
When adding new UI components, make sure to add dark theme styles:

```css
/* Light theme styles (default) */
.my-component {
  background: white;
  color: var(--rich-black);
}

/* Dark theme styles */
[data-theme="dark"] .my-component {
  background: var(--bg-card);
  color: var(--text-primary);
}
```

## CSS Variables Reference

### Light Theme Colors
- `--rich-black`: #0d1b2aff (dark text)
- `--oxford-blue`: #1b263bff
- `--yinmn-blue`: #415a77ff
- `--silver-lake-blue`: #778da9ff
- `--platinum`: #e0e1ddff (light background)

### Dark Theme Colors
- `--rich-black`: #e2e8f0 (light text)
- `--oxford-blue`: #4a5568
- `--yinmn-blue`: #718096
- `--silver-lake-blue`: #a0aec0
- `--platinum`: #1a202c (dark background)

### Semantic Variables (Dark Theme)
- `--bg-primary`: Main background color
- `--bg-secondary`: Secondary background color
- `--bg-tertiary`: Tertiary background color
- `--bg-card`: Card background color
- `--bg-input`: Input field background color
- `--text-primary`: Primary text color
- `--text-secondary`: Secondary text color
- `--text-muted`: Muted text color
- `--border-primary`: Primary border color
- `--border-secondary`: Secondary border color

## Testing

### Browser Testing
Open `theme-demo.html` in a browser to test theme functionality without Electron.

### Electron Testing
1. Run the app: `npm start`
2. Change your Windows theme setting
3. Observe automatic theme changes in the app
4. Test manual theme switching with the toggle button

### Development Mode
Run with `--dev` flag to open DevTools:
```bash
npm start -- --dev
```

## Troubleshooting

### Theme Not Changing
1. Check browser console for JavaScript errors
2. Verify `theme-manager.js` is loaded
3. Ensure CSS custom properties are properly defined

### Electron Theme Detection Not Working
1. Verify Electron version supports `nativeTheme` API (v6.0.0+)
2. Check that `preload.js` exposes the theme change handler
3. Ensure main process is sending theme change events

### Styles Not Applying
1. Check CSS selector specificity
2. Verify `[data-theme="dark"]` attribute is set on `<html>` element
3. Ensure CSS custom properties are defined for both themes

## Browser Compatibility

- **Modern Browsers**: Full support (Chrome 49+, Firefox 31+, Safari 9.1+)
- **CSS Custom Properties**: Required for theme system
- **localStorage**: Required for theme persistence
- **matchMedia**: Required for system theme detection

## Future Enhancements

- High contrast theme support
- Custom color themes
- Per-component theme overrides
- Theme transition animations
- Accessibility improvements
