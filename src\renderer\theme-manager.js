/**
 * Theme Manager for Bryzos Troubleshooting Tool
 * Handles automatic Windows theme detection and manual theme switching
 */

class ThemeManager {
  constructor() {
    this.currentTheme = 'auto';
    this.systemTheme = 'light';
    this.init();
  }

  /**
   * Initialize the theme manager
   */
  init() {
    // Load saved theme preference
    this.loadThemePreference();
    
    // Set up system theme detection
    this.setupSystemThemeDetection();
    
    // Apply initial theme
    this.applyTheme();
    
    // Set up theme toggle button
    this.setupThemeToggle();
    
    // Listen for system theme changes
    this.listenForSystemThemeChanges();
  }

  /**
   * Load theme preference from localStorage
   */
  loadThemePreference() {
    const savedTheme = localStorage.getItem('bryzos-theme');
    if (savedTheme && ['light', 'dark', 'auto'].includes(savedTheme)) {
      this.currentTheme = savedTheme;
    }
  }

  /**
   * Save theme preference to localStorage
   */
  saveThemePreference() {
    localStorage.setItem('bryzos-theme', this.currentTheme);
  }

  /**
   * Detect system theme preference
   */
  setupSystemThemeDetection() {
    if (window.matchMedia) {
      const darkModeQuery = window.matchMedia('(prefers-color-scheme: dark)');
      this.systemTheme = darkModeQuery.matches ? 'dark' : 'light';
    }
  }

  /**
   * Listen for system theme changes
   */
  listenForSystemThemeChanges() {
    if (window.matchMedia) {
      const darkModeQuery = window.matchMedia('(prefers-color-scheme: dark)');
      darkModeQuery.addEventListener('change', (e) => {
        this.systemTheme = e.matches ? 'dark' : 'light';
        if (this.currentTheme === 'auto') {
          this.applyTheme();
        }
      });
    }

    // For Electron apps, listen for native theme changes from main process
    if (window.electronAPI && window.electronAPI.onThemeChange) {
      window.electronAPI.onThemeChange((theme) => {
        console.log('Native theme changed to:', theme);
        this.systemTheme = theme;
        if (this.currentTheme === 'auto') {
          this.applyTheme();
        }
      });
    }
  }

  /**
   * Get the effective theme (resolves 'auto' to actual theme)
   */
  getEffectiveTheme() {
    if (this.currentTheme === 'auto') {
      return this.systemTheme;
    }
    return this.currentTheme;
  }

  /**
   * Apply the current theme to the document
   */
  applyTheme() {
    const effectiveTheme = this.getEffectiveTheme();
    const html = document.documentElement;
    
    // Remove existing theme attributes
    html.removeAttribute('data-theme');
    
    // Apply new theme
    if (effectiveTheme !== 'light') {
      html.setAttribute('data-theme', effectiveTheme);
    }
    
    // Update theme toggle button
    this.updateThemeToggleButton();
    
    // Dispatch theme change event
    this.dispatchThemeChangeEvent(effectiveTheme);
  }

  /**
   * Set theme manually
   */
  setTheme(theme) {
    if (!['light', 'dark', 'auto'].includes(theme)) {
      console.warn('Invalid theme:', theme);
      return;
    }
    
    this.currentTheme = theme;
    this.saveThemePreference();
    this.applyTheme();
  }

  /**
   * Toggle between light and dark themes
   */
  toggleTheme() {
    const effectiveTheme = this.getEffectiveTheme();
    const newTheme = effectiveTheme === 'light' ? 'dark' : 'light';
    this.setTheme(newTheme);
  }

  /**
   * Cycle through all theme options (light -> dark -> auto)
   */
  cycleTheme() {
    const themeOrder = ['light', 'dark', 'auto'];
    const currentIndex = themeOrder.indexOf(this.currentTheme);
    const nextIndex = (currentIndex + 1) % themeOrder.length;
    this.setTheme(themeOrder[nextIndex]);
  }

  /**
   * Set up theme toggle button
   */
  setupThemeToggle() {
    // Create theme toggle button if it doesn't exist
    let toggleButton = document.getElementById('theme-toggle');
    if (!toggleButton) {
      toggleButton = document.createElement('button');
      toggleButton.id = 'theme-toggle';
      toggleButton.className = 'theme-toggle';
      toggleButton.setAttribute('aria-label', 'Toggle theme');
      toggleButton.setAttribute('title', 'Toggle theme');
      document.body.appendChild(toggleButton);
    }

    // Add click event listener
    toggleButton.addEventListener('click', () => {
      this.cycleTheme();
    });

    this.updateThemeToggleButton();
  }

  /**
   * Update theme toggle button appearance
   */
  updateThemeToggleButton() {
    const toggleButton = document.getElementById('theme-toggle');
    if (!toggleButton) return;

    const effectiveTheme = this.getEffectiveTheme();
    
    // Update button icon and title based on current theme
    let icon, title;
    switch (this.currentTheme) {
      case 'light':
        icon = '☀️';
        title = 'Switch to dark theme';
        break;
      case 'dark':
        icon = '🌙';
        title = 'Switch to auto theme';
        break;
      case 'auto':
        icon = effectiveTheme === 'dark' ? '🌓' : '🌗';
        title = 'Switch to light theme (currently auto)';
        break;
      default:
        icon = '🎨';
        title = 'Toggle theme';
    }

    toggleButton.textContent = icon;
    toggleButton.setAttribute('title', title);
    toggleButton.setAttribute('aria-label', title);
  }

  /**
   * Dispatch theme change event
   */
  dispatchThemeChangeEvent(effectiveTheme) {
    const event = new CustomEvent('themechange', {
      detail: {
        theme: this.currentTheme,
        effectiveTheme: effectiveTheme,
        systemTheme: this.systemTheme
      }
    });
    document.dispatchEvent(event);
  }

  /**
   * Get current theme information
   */
  getThemeInfo() {
    return {
      currentTheme: this.currentTheme,
      effectiveTheme: this.getEffectiveTheme(),
      systemTheme: this.systemTheme
    };
  }
}

// Initialize theme manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.themeManager = new ThemeManager();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ThemeManager;
}
